#!/usr/bin/env python3
"""
Teste específico para diagnosticar problemas do TTL
"""

print("🔍 INICIANDO DIAGNÓSTICO TTL")
print("=" * 40)

try:
    print("1️⃣ Testando imports básicos...")
    import sys
    import os
    from pathlib import Path
    print("✅ Imports básicos OK")
    
    print("2️⃣ Testando carregamento de ambiente...")
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ Dotenv carregado")
    
    print("3️⃣ Testando importação do Supabase...")
    from supabase import create_client, Client
    print("✅ Supabase importado")
    
    print("4️⃣ Testando conexão Supabase...")
    SUPABASE_URL = os.getenv("SUPABASE_URL")
    SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY")
    
    if not SUPABASE_URL or not SUPABASE_ANON_KEY:
        print("❌ Variáveis de ambiente não encontradas")
        print(f"   SUPABASE_URL: {'✅' if SUPABASE_URL else '❌'}")
        print(f"   SUPABASE_ANON_KEY: {'✅' if SUPABASE_ANON_KEY else '❌'}")
    else:
        print("✅ Variáveis de ambiente encontradas")
        
        # Testa conexão
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
        print("✅ Cliente Supabase criado")
        
        print("5️⃣ Testando acesso às tabelas...")
        
        # Testa cada tabela
        tabelas = ['torneio_pontuacoes', 'torneios_ativos', 'torneio_recompensas_config']
        
        for tabela in tabelas:
            try:
                result = supabase.table(tabela).select('*').limit(1).execute()
                print(f"✅ Tabela {tabela}: {len(result.data)} registros")
            except Exception as e:
                print(f"❌ Tabela {tabela}: {e}")
        
        print("6️⃣ Testando importação do TTL...")
        try:
            from game.tournament_ttl import torneio_manager
            print("✅ TTL Manager importado")
            
            print("7️⃣ Testando função básica...")
            ranking = torneio_manager.get_ranking_deface()
            print(f"✅ Ranking obtido: {ranking}")
            
        except Exception as e:
            print(f"❌ Erro no TTL Manager: {e}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")

except Exception as e:
    print(f"❌ ERRO CRÍTICO: {e}")
    import traceback
    print(f"Traceback: {traceback.format_exc()}")

print("\n🔍 DIAGNÓSTICO CONCLUÍDO")
