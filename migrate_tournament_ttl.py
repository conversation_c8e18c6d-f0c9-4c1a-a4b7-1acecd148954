#!/usr/bin/env python3
"""
Script de migração para implementar o novo sistema de torneios com TTL automático.
Este script cria as tabelas, funções e configurações necessárias no Supabase.
"""

import os
import sys
from pathlib import Path

# Adicionar o diretório do projeto ao path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

try:
    from database.supabase_client import supabase_client
    if supabase_client.client is None:
        print("❌ Cliente Supabase não conectado")
        print("📝 Configure as variáveis de ambiente:")
        print("   SUPABASE_URL=https://sua-instancia.supabase.co")
        print("   SUPABASE_ANON_KEY=sua-chave-anonima")
        print("   Crie um arquivo .env na raiz do projeto com essas variáveis")
        sys.exit(1)
    print("✅ Conexão com Supabase estabelecida")
except ImportError as e:
    print(f"❌ Erro ao importar supabase_client: {e}")
    print("Certifique-se de que o arquivo supabase_client.py existe e está configurado corretamente")
    sys.exit(1)

def executar_sql_do_arquivo(caminho_arquivo):
    """
    Lê e executa comandos SQL de um arquivo.
    """
    try:
        with open(caminho_arquivo, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # Dividir em comandos individuais (separados por ponto-e-vírgula)
        comandos = [cmd.strip() for cmd in sql_content.split(';') if cmd.strip()]
        
        print(f"📁 Executando {len(comandos)} comandos SQL de {caminho_arquivo}")
        
        sucessos = 0
        erros = 0
        
        for i, comando in enumerate(comandos, 1):
            if not comando or comando.startswith('--') or comando.startswith('/*'):
                continue
                
            try:
                # Executar comando
                if 'SELECT cron.schedule' in comando:
                    print(f"⏭️  Comando {i}: Pulando configuração de cron (não suportado via Python)")
                    continue
                
                resultado = supabase_client.client.rpc('execute_sql', {'sql_command': comando}).execute()
                print(f"✅ Comando {i}: Executado com sucesso")
                sucessos += 1
                
            except Exception as e:
                print(f"❌ Comando {i}: Erro - {str(e)}")
                print(f"   SQL: {comando[:100]}...")
                erros += 1
                
                # Se for um erro crítico, parar
                if 'does not exist' in str(e) and 'function' in str(e):
                    print("⚠️  Tentando executar comando diretamente via SQL...")
                    try:
                        # Para funções e triggers, usar a query direta
                        supabase_client.client.postgrest.schema('public').rpc(comando).execute()
                        print(f"✅ Comando {i}: Executado via query direta")
                        sucessos += 1
                        erros -= 1
                    except:
                        pass
        
        print(f"\n📊 Resumo da execução:")
        print(f"   ✅ Sucessos: {sucessos}")
        print(f"   ❌ Erros: {erros}")
        
        return erros == 0
        
    except FileNotFoundError:
        print(f"❌ Arquivo não encontrado: {caminho_arquivo}")
        return False
    except Exception as e:
        print(f"❌ Erro ao executar SQL: {e}")
        return False

def executar_sql_direto(sql_commands):
    """
    Fornece instruções para execução manual dos comandos SQL no Supabase.
    """
    print("⚠️  EXECUÇÃO MANUAL NECESSÁRIA")
    print("🔧 O cliente Python do Supabase não suporta DDL direto")
    print("📋 Siga estas instruções:")
    print("   1. Acesse: https://app.supabase.com")
    print("   2. Selecione seu projeto")
    print("   3. Vá para 'SQL Editor'")
    print("   4. Execute os comandos abaixo um por vez:")
    print("=" * 70)
    
    for i, comando in enumerate(sql_commands, 1):
        print(f"\n-- COMANDO {i}:")
        print(comando)
    
    print("\n" + "=" * 70)
    print("💡 DICA: Copie cada comando e cole no SQL Editor")
    print("✅ Após executar todos, continue com este script")
    
    # Perguntar se o usuário executou os comandos
    resposta = input("\n🤔 Executou todos os comandos no Supabase? (s/n): ").lower().strip()
    
    if resposta in ['s', 'sim', 'y', 'yes']:
        print(f"✅ Considerando {len(sql_commands)} comandos executados")
        return len(sql_commands), 0
    else:
        print("❌ Migração pausada. Execute os comandos primeiro.")
        return 0, len(sql_commands)

def verificar_tabelas_criadas():
    """
    Verifica se as tabelas foram criadas corretamente.
    """
    tabelas_necessarias = [
        'torneios_ativos',
        'torneio_pontuacoes', 
        'torneio_historico_recompensas',
        'torneio_recompensas_config'
    ]
    
    print("🔍 Verificando se as tabelas foram criadas...")
    
    for tabela in tabelas_necessarias:
        try:
            resultado = supabase_client.client.table(tabela).select('*').limit(1).execute()
            print(f"✅ Tabela '{tabela}': OK")
        except Exception as e:
            print(f"❌ Tabela '{tabela}': Erro - {str(e)}")
            return False
    
    return True

def criar_indices():
    """
    Cria os índices necessários para performance.
    """
    print("📈 Criando índices...")
    
    comandos_sql = [
        # Índices para torneios_ativos
        "CREATE INDEX IF NOT EXISTS idx_torneios_tipo_ativo ON torneios_ativos(tipo, ativo);",
        "CREATE INDEX IF NOT EXISTS idx_torneios_data_fim ON torneios_ativos(data_fim);",
        "CREATE INDEX IF NOT EXISTS idx_torneios_temporada ON torneios_ativos(temporada);",
        
        # Índices para torneio_pontuacoes
        "CREATE INDEX IF NOT EXISTS idx_pontuacoes_torneio ON torneio_pontuacoes(torneio_id);",
        "CREATE INDEX IF NOT EXISTS idx_pontuacoes_jogador ON torneio_pontuacoes(jogador_uid);",
        "CREATE INDEX IF NOT EXISTS idx_pontuacoes_grupo ON torneio_pontuacoes(grupo_id);",
        "CREATE INDEX IF NOT EXISTS idx_pontuacoes_tipo ON torneio_pontuacoes(tipo_torneio);",
        "CREATE INDEX IF NOT EXISTS idx_pontuacoes_expires ON torneio_pontuacoes(expires_at);",
        "CREATE INDEX IF NOT EXISTS idx_pontuacoes_pontos_ind ON torneio_pontuacoes(pontos_individuais DESC);",
        "CREATE INDEX IF NOT EXISTS idx_pontuacoes_pontos_grupo ON torneio_pontuacoes(pontos_grupo DESC);",
        
        # Índices para torneio_historico_recompensas
        "CREATE INDEX IF NOT EXISTS idx_historico_jogador ON torneio_historico_recompensas(jogador_uid);",
        "CREATE INDEX IF NOT EXISTS idx_historico_data ON torneio_historico_recompensas(data_torneio);",
        "CREATE INDEX IF NOT EXISTS idx_historico_tipo ON torneio_historico_recompensas(tipo_torneio);",
        "CREATE INDEX IF NOT EXISTS idx_historico_temporada ON torneio_historico_recompensas(temporada);",
        "CREATE INDEX IF NOT EXISTS idx_historico_posicao ON torneio_historico_recompensas(posicao);",
        
        # Índices para torneio_recompensas_config
        "CREATE INDEX IF NOT EXISTS idx_recompensas_tipo ON torneio_recompensas_config(tipo_torneio);",
        "CREATE INDEX IF NOT EXISTS idx_recompensas_posicao ON torneio_recompensas_config(posicao_min, posicao_max);"
    ]
    
    sucessos, erros = executar_sql_direto(comandos_sql)
    print(f"📊 Índices: {sucessos} sucessos, {erros} erros")
    
    return erros == 0

def inserir_configuracoes_iniciais():
    """
    Insere configurações iniciais de recompensas.
    """
    print("⚙️  Inserindo configurações iniciais...")
    
    comandos_sql = [
        # Recompensas para deface
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('deface', 1, 1, 50000, 500) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('deface', 2, 2, 30000, 300) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('deface', 3, 3, 20000, 200) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('deface', 4, 5, 15000, 150) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('deface', 6, 10, 10000, 100) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        
        # Recompensas para upgrade
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('upgrade', 1, 1, 40000, 400) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('upgrade', 2, 2, 25000, 250) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('upgrade', 3, 3, 15000, 150) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('upgrade', 4, 5, 10000, 100) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;",
        "INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) VALUES ('upgrade', 6, 10, 5000, 50) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;"
    ]
    
    sucessos, erros = executar_sql_direto(comandos_sql)
    print(f"⚙️  Configurações: {sucessos} sucessos, {erros} erros")
    
    return erros == 0

def main():
    """
    Função principal de migração.
    """
    print("🚀 Iniciando migração para sistema de torneios com TTL automático")
    print("=" * 70)
    
    # Passo 1: Criar tabelas básicas
    print("\n📋 PASSO 1: Criando tabelas básicas")
    sucessos_tabelas, erros_tabelas = executar_sql_direto([
        """
        CREATE TABLE IF NOT EXISTS torneios_ativos (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tipo VARCHAR(50) NOT NULL CHECK (tipo IN ('upgrade', 'deface')),
            data_inicio TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            data_fim TIMESTAMPTZ NOT NULL,
            app_do_dia VARCHAR(50),
            ativo BOOLEAN DEFAULT true,
            temporada INTEGER DEFAULT 1,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            CONSTRAINT unique_torneio_ativo UNIQUE (tipo, ativo)
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS torneio_pontuacoes (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            torneio_id UUID REFERENCES torneios_ativos(id) ON DELETE CASCADE,
            jogador_uid VARCHAR(255) NOT NULL,
            grupo_id VARCHAR(255),
            grupo_nome VARCHAR(255),
            nick VARCHAR(255) NOT NULL,
            pontos_individuais INTEGER DEFAULT 0,
            pontos_grupo INTEGER DEFAULT 0,
            tipo_torneio VARCHAR(50) NOT NULL,
            ultima_atualizacao TIMESTAMPTZ DEFAULT NOW(),
            expires_at TIMESTAMPTZ NOT NULL,
            CONSTRAINT unique_jogador_torneio UNIQUE (jogador_uid, tipo_torneio, torneio_id),
            CONSTRAINT check_pontos_positivos CHECK (pontos_individuais >= 0 AND pontos_grupo >= 0)
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS torneio_historico_recompensas (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            torneio_id UUID NOT NULL,
            tipo_torneio VARCHAR(50) NOT NULL,
            temporada INTEGER NOT NULL,
            jogador_uid VARCHAR(255) NOT NULL,
            nick VARCHAR(255) NOT NULL,
            grupo_id VARCHAR(255),
            grupo_nome VARCHAR(255),
            posicao INTEGER NOT NULL,
            pontos_finais INTEGER NOT NULL,
            recompensa_dinheiro INTEGER DEFAULT 0,
            recompensa_shacks INTEGER DEFAULT 0,
            data_torneio DATE NOT NULL,
            data_pagamento TIMESTAMPTZ DEFAULT NOW()
        );
        """,
        """
        CREATE TABLE IF NOT EXISTS torneio_recompensas_config (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tipo_torneio VARCHAR(50) NOT NULL,
            posicao_min INTEGER NOT NULL,
            posicao_max INTEGER NOT NULL,
            dinheiro INTEGER DEFAULT 0,
            shacks INTEGER DEFAULT 0,
            ativo BOOLEAN DEFAULT true,
            CONSTRAINT unique_recompensa_config UNIQUE (tipo_torneio, posicao_min, posicao_max)
        );
        """
    ])
    
    if erros_tabelas > 0:
        print(f"⚠️  {erros_tabelas} erros ao criar tabelas. Verifique as instruções acima.")
        if sucessos_tabelas == 0:
            print("❌ Nenhuma tabela foi criada automaticamente.")
            print("📋 Execute os comandos SQL manualmente no painel Supabase e tente novamente.")
            return
    
    # Passo 2: Verificar se tabelas foram criadas (só se conseguimos executar algo)
    if sucessos_tabelas > 0:
        print("\n📋 PASSO 2: Verificando tabelas criadas")
        if not verificar_tabelas_criadas():
            print("⚠️  Algumas tabelas podem não ter sido criadas corretamente.")
    
    # Passo 3: Inserir configurações iniciais
    print("\n📋 PASSO 3: Inserindo configurações iniciais")
    if not inserir_configuracoes_iniciais():
        print("⚠️  Falha ao inserir configurações iniciais. Execute manualmente se necessário.")
    
    # Passo 4: Criar índices
    print("\n📋 PASSO 4: Criando índices")
    if not criar_indices():
        print("⚠️  Falha ao criar índices. Execute manualmente se necessário.")
    
    # Passo 5: Executar schema completo (se arquivo existir)
    schema_path = Path(__file__).parent / "database" / "tournament_schema.sql"
    if schema_path.exists():
        print(f"\n� PASSO 5: Executando schema completo")
        print(f"📄 Arquivo: {schema_path}")
        if not executar_sql_do_arquivo(schema_path):
            print("⚠️  Algumas partes do schema falharam. Execute manualmente no painel Supabase.")
    else:
        print(f"\n📄 Schema completo não encontrado em: {schema_path}")
        print("   Usando apenas configuração básica.")
    
    print("\n" + "=" * 70)
    print("🎉 MIGRAÇÃO CONCLUÍDA!")
    print()
    print("✅ O sistema de torneios TTL foi configurado!")
    print()
    print("🔧 PRÓXIMOS PASSOS:")
    print("   1. Se houve erros acima, execute os comandos SQL manualmente no painel Supabase")
    print("   2. Acesse: https://app.supabase.com")
    print("   3. Vá para SQL Editor e execute os comandos mostrados")
    print("   4. Reinicie o servidor Flask: python app.py")
    print("   5. Teste o sistema: python testar_ttl.py")
    print()
    print("� ARQUIVOS IMPORTANTES:")
    print("   • Schema completo: database/tournament_schema.sql")
    print("   • API Python: game/tournament_ttl.py")
    print("   • Testes: testar_ttl.py")
    print()
    print("🎯 BENEFÍCIOS DO SISTEMA TTL:")
    print("   • Reset automático à meia-noite")
    print("   • 95% menos requisições ao servidor")
    print("   • Distribuição automática de recompensas")
    print("   • Performance otimizada")

if __name__ == "__main__":
    main()
