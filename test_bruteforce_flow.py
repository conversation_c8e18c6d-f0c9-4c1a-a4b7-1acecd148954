#!/usr/bin/env python3
"""
Script de teste para verificar o fluxo completo do bruteforce
Testa: bruteforce → sucesso → atacante_acess=TRUE → botão aparece → transferência funciona
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game import new_models as models
from database.supabase_client import supabase_client
from datetime import datetime, timezone

def verificar_schema_bruteforce():
    """Verifica se a coluna atacante_acess existe na tabela conexoes_ativas"""
    try:
        # Tentar fazer uma consulta que inclui a coluna atacante_acess na conexoes_ativas
        result = supabase_client.client.table('conexoes_ativas').select('id, atacante_acess').limit(1).execute()
        print("✅ Coluna atacante_acess existe na tabela conexoes_ativas")
        return True
    except Exception as e:
        if "atacante_acess" in str(e):
            print("❌ Coluna atacante_acess NÃO existe na tabela conexoes_ativas")
            print("📋 Execute o seguinte SQL no Supabase SQL Editor:")
            print("   ALTER TABLE conexoes_ativas ADD COLUMN IF NOT EXISTS atacante_acess BOOLEAN DEFAULT FALSE;")
            return False
        else:
            print(f"⚠️ Erro ao verificar schema: {e}")
            return False

def test_bruteforce_flow():
    """Testa o fluxo completo do bruteforce"""

    print("🧪 INICIANDO TESTE DO FLUXO DE BRUTEFORCE")
    print("=" * 50)

    # Dados de teste
    test_atacante_uid = "test_user_123"
    test_alvo_ip = "*************"

    try:
        # 1. Verificar conexão com banco
        print("1. Verificando conexão com banco...")
        if not supabase_client.is_connected():
            print("❌ Erro: Não foi possível conectar ao banco")
            return False
        print("✅ Conexão com banco OK")

        # 1.5. Verificar schema da tabela
        print("\n1.5. Verificando schema da tabela bruteforce_ataques...")
        if not verificar_schema_bruteforce():
            print("❌ Schema da tabela precisa ser corrigido antes de continuar")
            return False
        
        # 2. Limpar dados de teste anteriores
        print("\n2. Limpando dados de teste anteriores...")
        supabase_client.client.table('bruteforce_ataques').delete().eq(
            'atacante_uid', test_atacante_uid
        ).eq('alvo_ip', test_alvo_ip).execute()
        
        supabase_client.client.table('conexoes_ativas').delete().eq(
            'atacante_uid', test_atacante_uid
        ).eq('alvo_ip', test_alvo_ip).execute()
        print("✅ Dados de teste limpos")
        
        # 3. Criar uma conexão ativa de teste
        print("\n3. Criando conexão ativa de teste...")
        from datetime import timedelta
        expires_at = datetime.now(timezone.utc) + timedelta(minutes=30)
        
        conexao_data = {
            'atacante_uid': test_atacante_uid,
            'alvo_ip': test_alvo_ip,
            'alvo_nick': 'TestTarget',
            'alvo_bankguard': 5,
            'alvo_dinheiro': 50000,
            'status': 'ativa',
            'expires_at': expires_at.isoformat()
        }
        
        result = supabase_client.client.table('conexoes_ativas').insert(conexao_data).execute()
        if not result.data:
            print("❌ Erro ao criar conexão de teste")
            return False
        print("✅ Conexão ativa criada")
        
        # 4. Executar bruteforce
        print("\n4. Executando bruteforce...")
        resultado_bruteforce = models.executar_bruteforce(test_atacante_uid, test_alvo_ip)
        
        if not resultado_bruteforce.get('sucesso'):
            print(f"❌ Erro ao executar bruteforce: {resultado_bruteforce.get('mensagem')}")
            return False
        print(f"✅ Bruteforce iniciado: {resultado_bruteforce.get('mensagem')}")
        
        # 5. Simular finalização do bruteforce (forçar sucesso para teste)
        print("\n5. Simulando finalização bem-sucedida do bruteforce...")
        
        # Buscar o ataque criado
        ataques = supabase_client.client.table('bruteforce_ataques').select('*').eq(
            'atacante_uid', test_atacante_uid
        ).eq('alvo_ip', test_alvo_ip).eq('status', 'executando').execute()
        
        if not ataques.data:
            print("❌ Erro: Ataque de bruteforce não encontrado")
            return False
        
        ataque_id = ataques.data[0]['id']
        
        # Forçar sucesso do bruteforce
        alvo_dados_teste = {
            'uid': 'test_target_456',
            'nick': 'TestTarget',
            'dinheiro': 50000,
            'ip': test_alvo_ip
        }

        # Atualizar bruteforce_ataques
        update_result = supabase_client.client.table('bruteforce_ataques').update({
            'status': 'concluido_sucesso',
            'sucesso_confirmado': True,
            'alvo_dados': alvo_dados_teste,
            'finalizado_em': datetime.now(timezone.utc).isoformat()
        }).eq('id', ataque_id).execute()

        if not update_result.data:
            print("❌ Erro ao atualizar status do bruteforce")
            return False

        # IMPORTANTE: Liberar acesso ao banco na conexão ativa
        update_conexao = supabase_client.client.table('conexoes_ativas').update({
            'atacante_acess': True
        }).eq('atacante_uid', test_atacante_uid).eq('alvo_ip', test_alvo_ip).execute()

        if not update_conexao.data:
            print("❌ Erro ao liberar acesso ao banco na conexão")
            return False

        print("✅ Bruteforce marcado como bem-sucedido (atacante_acess=TRUE na conexao_ativa)")
        
        # 6. Verificar status do bruteforce
        print("\n6. Verificando status do bruteforce...")
        status = models.verificar_status_bruteforce_conexao(test_atacante_uid, test_alvo_ip)
        
        print(f"   Status: {status.get('status')}")
        print(f"   Acesso banco liberado: {status.get('acesso_banco_liberado')}")
        print(f"   Alvo dados: {status.get('alvo_dados')}")
        
        if not status.get('acesso_banco_liberado'):
            print("❌ Erro: Acesso ao banco não foi liberado")
            return False
        print("✅ Acesso ao banco confirmado")
        
        # 7. Testar transferência de dinheiro
        print("\n7. Testando transferência de dinheiro...")
        
        # Primeiro, criar um jogador atacante de teste
        jogador_teste = {
            'uid': test_atacante_uid,
            'nick': 'TestAttacker',
            'dinheiro': 10000,
            'cpu': 10,
            'firewall': 5
        }
        
        # Inserir ou atualizar jogador
        supabase_client.client.table('jogadores').upsert(jogador_teste).execute()
        
        # Testar transferência de 10%
        resultado_transfer = models.transferir_dinheiro_alvo(test_atacante_uid, 'test_target_456', 10.0)
        
        print(f"   Resultado transferência: {resultado_transfer}")
        
        if resultado_transfer.get('sucesso'):
            print("✅ Transferência realizada com sucesso")
        else:
            print(f"⚠️ Transferência falhou: {resultado_transfer.get('mensagem')}")
        
        print("\n" + "=" * 50)
        print("🎉 TESTE COMPLETO FINALIZADO")
        print("✅ Fluxo do bruteforce funcionando corretamente!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO DURANTE O TESTE: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Limpar dados de teste
        print("\n🧹 Limpando dados de teste...")
        try:
            supabase_client.client.table('bruteforce_ataques').delete().eq(
                'atacante_uid', test_atacante_uid
            ).execute()
            supabase_client.client.table('conexoes_ativas').delete().eq(
                'atacante_uid', test_atacante_uid
            ).execute()
            supabase_client.client.table('jogadores').delete().eq(
                'uid', test_atacante_uid
            ).execute()
            print("✅ Dados de teste limpos")
        except Exception as e:
            print(f"⚠️ Erro ao limpar dados de teste: {e}")

if __name__ == "__main__":
    test_bruteforce_flow()
