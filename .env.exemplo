# 🚀 CONFIGURAÇÃO RÁPIDA DO SISTEMA TTL
# =====================================

# 1. Configure as variáveis de ambiente do Supabase
# Crie ou edite o arquivo .env na raiz do projeto:

SUPABASE_URL=https://sua-instancia.supabase.co
SUPABASE_ANON_KEY=sua-chave-anonima-aqui

# 2. Substitua pelos seus valores reais do painel do Supabase

# 3. Execute a migração das tabelas:
# python migrate_tournament_ttl.py

# 4. Execute o schema SQL no painel do Supabase:
# Cole o conteúdo de database/tournament_schema.sql no SQL Editor

# 5. Teste o sistema:
# python testar_ttl.py

# 📊 STATUS ATUAL:
# ✅ Sistema TTL implementado e funcional
# ✅ Modo fallback ativo (cache local temporário)
# ⚠️  Aguardando configuração do Supabase para modo completo
# ✅ Todas as APIs de torneio atualizadas
# ✅ Sistema de debug implementado

# 🎯 PROBLEMA IDENTIFICADO:
# O deface está sendo executado mas os pontos não aparecem no ranking
# porque o Supabase não está configurado, então o sistema está usando
# cache local temporário que não persiste entre reinicios.

# 💡 SOLUÇÃO:
# Configure as variáveis SUPABASE_URL e SUPABASE_ANON_KEY
# e execute a migração para ativar o sistema completo.
