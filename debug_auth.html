<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Autenticação - SHACK</title>
    <style>
        body { 
            font-family: monospace; 
            background: #0f172a; 
            color: #f8fafc; 
            padding: 20px; 
            margin: 0;
        }
        .debug-box { 
            background: #1e293b; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px; 
            border: 1px solid #334155;
        }
        .error { color: #ef4444; }
        .success { color: #10b981; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
    </style>
</head>
<body>
    <h1>🔍 Debug Autenticação SHACK</h1>
    
    <div class="debug-box">
        <h3>📊 Status do Sistema</h3>
        <div id="status-output">Carregando...</div>
    </div>
    
    <div class="debug-box">
        <h3>🔐 Teste de Autenticação</h3>
        <button onclick="testAuth()">Testar Autenticação</button>
        <button onclick="clearSession()">Limpar Sessão</button>
        <button onclick="checkLocalStorage()">Verificar localStorage</button>
        <div id="auth-output"></div>
    </div>
    
    <div class="debug-box">
        <h3>🌐 Teste de API</h3>
        <button onclick="testAPI()">Testar /api/jogador</button>
        <div id="api-output"></div>
    </div>

    <!-- Carrega o sistema de autenticação -->
    <script src="/static/js/simple-auth.js"></script>
    
    <script>
        // Aguarda carregamento
        setTimeout(() => {
            document.getElementById('status-output').innerHTML = `
                <div class="info">✅ simple-auth.js carregado</div>
                <div class="info">✅ window.auth disponível: ${!!window.auth}</div>
                <div class="info">✅ Sessão atual: ${JSON.stringify(window.auth?.getSession(), null, 2)}</div>
            `;
        }, 500);
        
        function testAuth() {
            const output = document.getElementById('auth-output');
            
            try {
                const session = window.auth.getSession();
                
                if (session.user) {
                    output.innerHTML = `<div class="success">✅ Usuário logado: ${session.user.nick}</div>`;
                } else {
                    output.innerHTML = `<div class="warning">⚠️ Usuário não logado</div>`;
                }
                
            } catch (error) {
                output.innerHTML = `<div class="error">❌ Erro: ${error.message}</div>`;
            }
        }
        
        function clearSession() {
            window.auth.logout();
            location.reload();
        }
        
        function checkLocalStorage() {
            const user = localStorage.getItem('shack_user');
            const token = localStorage.getItem('shack_token');
            
            document.getElementById('auth-output').innerHTML = `
                <div class="info">User localStorage: ${user || 'Vazio'}</div>
                <div class="info">Token localStorage: ${token || 'Vazio'}</div>
            `;
        }
        
        async function testAPI() {
            const output = document.getElementById('api-output');
            
            try {
                const response = await fetch('/api/jogador');
                const data = await response.json();
                
                if (response.ok) {
                    output.innerHTML = `<div class="success">✅ API funcionando: ${JSON.stringify(data, null, 2)}</div>`;
                } else {
                    output.innerHTML = `<div class="error">❌ Erro API: ${data.mensagem}</div>`;
                }
                
            } catch (error) {
                output.innerHTML = `<div class="error">❌ Erro: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
