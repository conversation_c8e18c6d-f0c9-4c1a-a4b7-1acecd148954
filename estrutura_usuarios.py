#!/usr/bin/env python3
"""
VERIFICAR ESTRUTURA REAL DA TABELA USUARIOS
"""

print("🔍 VERIFICANDO ESTRUTURA DA TABELA USUARIOS")
print("=" * 50)

try:
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    
    from database.supabase_client import supabase_client
    
    supabase = supabase_client.client
    
    # 1. Verificar estrutura da tabela usuarios
    print("1️⃣ Testando campos disponíveis...")
    
    # Testar diferentes combinações de campos
    campos_teste = [
        'uid, nick',
        'uid, nick, grupo_id', 
        'uid, nick, grupo_id, grupo',
        'uid, nick, grupo_id, group_name',
        'uid, nick, grupo_id, team',
        'uid, nick, grupo_id, team_name'
    ]
    
    for campos in campos_teste:
        try:
            resultado = supabase.table('usuarios').select(campos).limit(1).execute()
            if resultado.data:
                print(f"✅ Campos '{campos}' funcionam")
                print(f"   Exemplo: {resultado.data[0]}")
            else:
                print(f"⚠️  Campos '{campos}' funcionam mas sem dados")
        except Exception as e:
            print(f"❌ Campos '{campos}' não funcionam: {e}")
    
    # 2. Verificar dados de usuários específicos  
    print(f"\n2️⃣ Verificando usuários específicos...")
    
    # Pegar UIDs dos dados de teste
    uids_teste = ['f47ac10b-58cc-4372-a567-0e02b2c3d479', 'dc35ba2e-7f41-4248-9f81-24a34529f901']
    
    for uid in uids_teste:
        try:
            usuario = supabase.table('usuarios').select('*').eq('uid', uid).limit(1).execute()
            if usuario.data:
                print(f"✅ Usuario {uid[:8]}...")
                for key, value in usuario.data[0].items():
                    if 'grupo' in key.lower() or 'team' in key.lower():
                        print(f"   {key}: {value}")
            else:
                print(f"❌ Usuario {uid[:8]}... não encontrado")
        except Exception as e:
            print(f"❌ Erro ao buscar {uid[:8]}...: {e}")
    
    print("\n🎯 ESTRUTURA VERIFICADA!")
    
except Exception as e:
    print(f"❌ Erro geral: {e}")
    import traceback
    traceback.print_exc()
