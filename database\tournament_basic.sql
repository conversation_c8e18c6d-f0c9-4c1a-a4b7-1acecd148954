-- ==========================================
-- SISTEMA DE TORNEIOS TTL - VERSÃO BÁSICA
-- Execute estes comandos no SQL Editor do Supabase
-- ==========================================

-- ============ STEP 1: TABELAS BÁSICAS ============

-- Tabela de torneios ativos
CREATE TABLE IF NOT EXISTS torneios_ativos (
    id SERIAL PRIMARY KEY,
    tipo VARCHAR(50) NOT NULL,
    ativo BOOLEAN DEFAULT true,
    data_inicio TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    data_fim TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Ta<PERSON>a de pontuações com TTL
CREATE TABLE IF NOT EXISTS torneio_pontuacoes (
    id SERIAL PRIMARY KEY,
    torneio_id INTEGER,
    jogador_uid VARCHAR(100) NOT NULL,
    pontos_individuais INTEGER DEFAULT 0,
    pontos_grupo INTEGER DEFAULT 0,
    tipo_torneio VARCHAR(50) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de configuração de recompensas
CREATE TABLE IF NOT EXISTS torneio_recompensas_config (
    id SERIAL PRIMARY KEY,
    tipo_torneio VARCHAR(50) NOT NULL,
    posicao_min INTEGER NOT NULL,
    posicao_max INTEGER NOT NULL,
    dinheiro INTEGER DEFAULT 0,
    shacks INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============ STEP 2: FUNÇÃO TTL ============

-- Função para atualizar TTL automaticamente
CREATE OR REPLACE FUNCTION atualizar_ttl_automatico()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Atualizar TTL baseado no tipo de torneio
    CASE NEW.tipo_torneio
        WHEN 'deface' THEN
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '24 hours';
        WHEN 'upgrade' THEN
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '168 hours';
        ELSE
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '24 hours';
    END CASE;
    
    RETURN NEW;
END;
$$;

-- ============ STEP 3: TRIGGER ============

-- Trigger para TTL automático
DROP TRIGGER IF EXISTS trigger_ttl_automatico ON torneio_pontuacoes;
CREATE TRIGGER trigger_ttl_automatico
    BEFORE INSERT OR UPDATE ON torneio_pontuacoes
    FOR EACH ROW
    EXECUTE FUNCTION atualizar_ttl_automatico();

-- ============ STEP 4: ÍNDICES ============

CREATE INDEX IF NOT EXISTS idx_torneios_tipo ON torneios_ativos(tipo);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_jogador ON torneio_pontuacoes(jogador_uid);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_tipo ON torneio_pontuacoes(tipo_torneio);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_expires ON torneio_pontuacoes(expires_at);

-- ============ STEP 5: RECOMPENSAS ============

-- Limpar recompensas existentes primeiro
DELETE FROM torneio_recompensas_config WHERE tipo_torneio IN ('deface', 'upgrade');

-- Recompensas deface
INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 1, 1, 50000, 500);

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 2, 2, 30000, 300);

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 3, 3, 20000, 200);

-- Recompensas upgrade
INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 1, 1, 40000, 400);

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 2, 2, 25000, 250);

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 3, 3, 15000, 150);

-- ============ STEP 6: TORNEIOS INICIAIS ============

-- Limpar torneios existentes primeiro
DELETE FROM torneios_ativos WHERE tipo IN ('deface', 'upgrade');

-- Torneio de deface (24h)
INSERT INTO torneios_ativos (tipo, data_fim)
VALUES ('deface', CURRENT_TIMESTAMP + INTERVAL '24 hours');

-- Torneio de upgrade (7 dias)
INSERT INTO torneios_ativos (tipo, data_fim)
VALUES ('upgrade', CURRENT_TIMESTAMP + INTERVAL '7 days');

-- ==========================================
-- SETUP BÁSICO - SISTEMA TTL PRONTO! 🚀
-- ==========================================
