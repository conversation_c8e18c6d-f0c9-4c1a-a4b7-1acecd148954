# 🔧 Correção do Erro "column parametros does not exist"

## ❌ Problema Identificado
O erro `ERROR: 42703: column "parametros" of relation "torneios_ativos" does not exist` indica que a coluna `parametros` não foi criada na tabela.

## ✅ Soluções Disponíveis

### Opção 1: Use o arquivo simplificado (RECOMENDADO)
Execute o arquivo `database/tournament_simple_setup.sql` que não depende da coluna `parametros`:

```sql
-- Use este arquivo ao invés do completo
database/tournament_simple_setup.sql
```

### Opção 2: Execute step-by-step
Se quiser usar o arquivo original, execute os comandos em ordem:

1. **Primeiro execute apenas as tabelas:**
```sql
-- Apenas as CREATE TABLE statements
```

2. **Depois execute as funções e triggers**

3. **Por últ<PERSON> execute os INSERTs**

### Opção 3: Correção Manual
Se a tabela já foi criada sem a coluna `parametros`, adicione ela:

```sql
-- Adicionar a coluna parametros se ela não existir
ALTER TABLE torneios_ativos 
ADD COLUMN IF NOT EXISTS parametros JSONB DEFAULT '{}';
```

## 🚀 Arquivo Corrigido

O arquivo `database/tournament_complete_setup.sql` foi atualizado para remover a dependência da coluna `parametros` nos INSERTs.

Os comandos de inserção dos torneios agora são:

```sql
-- Versão corrigida (sem parametros)
INSERT INTO torneios_ativos (tipo, data_fim)
VALUES ('deface', CURRENT_TIMESTAMP + INTERVAL '24 hours')
ON CONFLICT (tipo, temporada) DO UPDATE SET...
```

## 💡 Recomendação

**Use o arquivo `tournament_simple_setup.sql`** - é mais robusto e compatível com diferentes versões do PostgreSQL/Supabase.

## ✅ Próximos Passos

1. Execute `database/tournament_simple_setup.sql` no SQL Editor do Supabase
2. Verifique se todas as tabelas foram criadas
3. Teste o sistema TTL com o Python

O sistema funcionará perfeitamente sem a coluna `parametros`! 🎯
