-- ==========================================
-- SISTEMA DE TORNEIOS TTL - SETUP COMPLETO
-- Execute estes comandos no SQL Editor do Supabase
-- ==========================================

-- ============ STEP 1: TABELAS BÁSICAS ============

-- Tabela de torneios ativos
CREATE TABLE IF NOT EXISTS torneios_ativos (
    id SERIAL PRIMARY KEY,
    tipo VARCHAR(50) NOT NULL,
    ativo BOOLEAN DEFAULT true,
    data_inicio TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    data_fim TIMESTAMP WITH TIME ZONE NOT NULL,
    temporada VARCHAR(20) DEFAULT to_char(CURRENT_TIMESTAMP, 'YYYY-MM'),
    parametros JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tipo, temporada)
);

-- Tabela de pontuações com TTL
CREATE TABLE IF NOT EXISTS torneio_pontuacoes (
    id SERIAL PRIMARY KEY,
    torneio_id INTEGER REFERENCES torneios_ativos(id) ON DELETE CASCADE,
    jogador_uid VARCHAR(100) NOT NULL,
    grupo_id VARCHAR(100),
    pontos_individuais INTEGER DEFAULT 0,
    pontos_grupo INTEGER DEFAULT 0,
    tipo_torneio VARCHAR(50) NOT NULL,
    ultima_atividade TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(torneio_id, jogador_uid)
);

-- Tabela de histórico de recompensas
CREATE TABLE IF NOT EXISTS torneio_historico_recompensas (
    id SERIAL PRIMARY KEY,
    jogador_uid VARCHAR(100) NOT NULL,
    tipo_torneio VARCHAR(50) NOT NULL,
    posicao INTEGER NOT NULL,
    pontos_finais INTEGER NOT NULL,
    recompensas JSONB NOT NULL,
    data_torneio TIMESTAMP WITH TIME ZONE NOT NULL,
    temporada VARCHAR(20) NOT NULL,
    processado BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de configuração de recompensas
CREATE TABLE IF NOT EXISTS torneio_recompensas_config (
    id SERIAL PRIMARY KEY,
    tipo_torneio VARCHAR(50) NOT NULL,
    posicao_min INTEGER NOT NULL,
    posicao_max INTEGER NOT NULL,
    dinheiro INTEGER DEFAULT 0,
    shacks INTEGER DEFAULT 0,
    itens JSONB DEFAULT '{}',
    ativo BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tipo_torneio, posicao_min, posicao_max)
);

-- ============ STEP 2: FUNÇÕES AUTOMÁTICAS ============

-- Função para limpar dados expirados
CREATE OR REPLACE FUNCTION limpar_dados_ttl()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    registros_removidos INTEGER;
BEGIN
    DELETE FROM torneio_pontuacoes 
    WHERE expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS registros_removidos = ROW_COUNT;
    
    RETURN registros_removidos;
END;
$$;

-- Função para distribuir recompensas automaticamente
CREATE OR REPLACE FUNCTION distribuir_recompensas_automatico()
RETURNS TABLE(
    jogador_uid VARCHAR(100),
    tipo_torneio VARCHAR(50),
    posicao INTEGER,
    recompensas JSONB
)
LANGUAGE plpgsql
AS $$
DECLARE
    torneio RECORD;
    ranking RECORD;
    pos INTEGER;
    recompensa RECORD;
BEGIN
    -- Para cada torneio que expirou
    FOR torneio IN 
        SELECT DISTINCT t.tipo, t.temporada
        FROM torneios_ativos t
        WHERE t.data_fim < CURRENT_TIMESTAMP 
        AND t.ativo = true
    LOOP
        pos := 1;
        
        -- Ranking dos jogadores
        FOR ranking IN
            SELECT p.jogador_uid, 
                   SUM(p.pontos_individuais) as total_pontos,
                   t.tipo as tipo_torneio
            FROM torneio_pontuacoes p
            JOIN torneios_ativos t ON p.torneio_id = t.id
            WHERE t.tipo = torneio.tipo 
            AND t.temporada = torneio.temporada
            GROUP BY p.jogador_uid, t.tipo
            ORDER BY total_pontos DESC
        LOOP
            -- Buscar recompensa para a posição
            SELECT INTO recompensa *
            FROM torneio_recompensas_config
            WHERE tipo_torneio = ranking.tipo_torneio
            AND pos >= posicao_min AND pos <= posicao_max
            AND ativo = true
            LIMIT 1;
            
            IF FOUND THEN
                -- Inserir no histórico
                INSERT INTO torneio_historico_recompensas (
                    jogador_uid, tipo_torneio, posicao, pontos_finais,
                    recompensas, data_torneio, temporada
                ) VALUES (
                    ranking.jogador_uid,
                    ranking.tipo_torneio,
                    pos,
                    ranking.total_pontos,
                    jsonb_build_object(
                        'dinheiro', recompensa.dinheiro,
                        'shacks', recompensa.shacks,
                        'itens', recompensa.itens
                    ),
                    CURRENT_TIMESTAMP,
                    torneio.temporada
                );
                
                -- Retornar resultado
                jogador_uid := ranking.jogador_uid;
                tipo_torneio := ranking.tipo_torneio;
                posicao := pos;
                recompensas := jsonb_build_object(
                    'dinheiro', recompensa.dinheiro,
                    'shacks', recompensa.shacks,
                    'itens', recompensa.itens
                );
                RETURN NEXT;
            END IF;
            
            pos := pos + 1;
        END LOOP;
        
        -- Desativar torneio
        UPDATE torneios_ativos 
        SET ativo = false 
        WHERE tipo = torneio.tipo 
        AND temporada = torneio.temporada;
    END LOOP;
    
    RETURN;
END;
$$;

-- Função para atualizar TTL automaticamente
CREATE OR REPLACE FUNCTION atualizar_ttl_automatico()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Atualizar TTL baseado no tipo de torneio
    CASE NEW.tipo_torneio
        WHEN 'deface' THEN
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '24 hours';
        WHEN 'upgrade' THEN
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '168 hours'; -- 7 dias
        ELSE
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '24 hours';
    END CASE;
    
    NEW.updated_at := CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$;

-- ============ STEP 3: TRIGGERS ============

-- Trigger para TTL automático
DROP TRIGGER IF EXISTS trigger_ttl_automatico ON torneio_pontuacoes;
CREATE TRIGGER trigger_ttl_automatico
    BEFORE INSERT OR UPDATE ON torneio_pontuacoes
    FOR EACH ROW
    EXECUTE FUNCTION atualizar_ttl_automatico();

-- ============ STEP 4: ÍNDICES DE PERFORMANCE ============

-- Índices para torneios_ativos
CREATE INDEX IF NOT EXISTS idx_torneios_tipo_ativo ON torneios_ativos(tipo, ativo);
CREATE INDEX IF NOT EXISTS idx_torneios_data_fim ON torneios_ativos(data_fim);
CREATE INDEX IF NOT EXISTS idx_torneios_temporada ON torneios_ativos(temporada);

-- Índices para torneio_pontuacoes
CREATE INDEX IF NOT EXISTS idx_pontuacoes_torneio ON torneio_pontuacoes(torneio_id);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_jogador ON torneio_pontuacoes(jogador_uid);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_grupo ON torneio_pontuacoes(grupo_id);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_tipo ON torneio_pontuacoes(tipo_torneio);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_expires ON torneio_pontuacoes(expires_at);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_pontos_ind ON torneio_pontuacoes(pontos_individuais DESC);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_pontos_grupo ON torneio_pontuacoes(pontos_grupo DESC);

-- Índices para torneio_historico_recompensas
CREATE INDEX IF NOT EXISTS idx_historico_jogador ON torneio_historico_recompensas(jogador_uid);
CREATE INDEX IF NOT EXISTS idx_historico_data ON torneio_historico_recompensas(data_torneio);
CREATE INDEX IF NOT EXISTS idx_historico_tipo ON torneio_historico_recompensas(tipo_torneio);
CREATE INDEX IF NOT EXISTS idx_historico_temporada ON torneio_historico_recompensas(temporada);
CREATE INDEX IF NOT EXISTS idx_historico_posicao ON torneio_historico_recompensas(posicao);

-- Índices para torneio_recompensas_config
CREATE INDEX IF NOT EXISTS idx_recompensas_tipo ON torneio_recompensas_config(tipo_torneio);
CREATE INDEX IF NOT EXISTS idx_recompensas_posicao ON torneio_recompensas_config(posicao_min, posicao_max);

-- ============ STEP 5: CONFIGURAÇÕES INICIAIS ============

-- Recompensas para deface
INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 1, 1, 50000, 500) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 2, 2, 30000, 300) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 3, 3, 20000, 200) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 4, 5, 15000, 150) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 6, 10, 10000, 100) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

-- Recompensas para upgrade
INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 1, 1, 40000, 400) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 2, 2, 25000, 250) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 3, 3, 15000, 150) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 4, 5, 10000, 100) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 6, 10, 5000, 50) ON CONFLICT (tipo_torneio, posicao_min, posicao_max) DO NOTHING;

-- ============ STEP 6: TORNEIOS INICIAIS ============

-- Torneio de deface (24h)
INSERT INTO torneios_ativos (tipo, data_fim)
VALUES (
    'deface',
    CURRENT_TIMESTAMP + INTERVAL '24 hours'
) ON CONFLICT (tipo, temporada) DO UPDATE SET
    data_fim = EXCLUDED.data_fim,
    ativo = true,
    updated_at = CURRENT_TIMESTAMP;

-- Torneio de upgrade (7 dias)
INSERT INTO torneios_ativos (tipo, data_fim)
VALUES (
    'upgrade',
    CURRENT_TIMESTAMP + INTERVAL '7 days'
) ON CONFLICT (tipo, temporada) DO UPDATE SET
    data_fim = EXCLUDED.data_fim,
    ativo = true,
    updated_at = CURRENT_TIMESTAMP;

-- ==========================================
-- SETUP COMPLETO - SISTEMA TTL PRONTO! 🚀
-- ==========================================
