-- ==========================================
-- CORREÇÃO DA TABELA CONEXOES_ATIVAS
-- Execute este comando no SQL Editor do Supabase
-- ==========================================

-- Adicionar coluna atacante_acess se não existir (default FALSE)
ALTER TABLE conexoes_ativas 
ADD COLUMN IF NOT EXISTS atacante_acess BOOLEAN DEFAULT FALSE;

-- Criar índice para performance
CREATE INDEX IF NOT EXISTS idx_conexoes_atacante_acess 
ON conexoes_ativas(atacante_acess);

-- Comentário para documentação
COMMENT ON COLUMN conexoes_ativas.atacante_acess IS 'TRUE quando o bruteforce foi bem-sucedido e o atacante tem acesso ao banco do alvo';

-- Verificar estrutura da tabela
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'conexoes_ativas' 
ORDER BY ordinal_position;
