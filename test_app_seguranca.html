<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste App Segurança</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --surface-elevated: #1f2937;
            --border-color: #374151;
            --primary-text: #f9fafb;
            --secondary-text: #9ca3af;
            --accent-blue: #3b82f6;
        }
        
        .bg-surface-elevated { background-color: var(--surface-elevated); }
        .border-border-color { border-color: var(--border-color); }
        .text-primary-text { color: var(--primary-text); }
        .text-secondary-text { color: var(--secondary-text); }
        .text-accent-blue { color: var(--accent-blue); }
        
        body { background-color: #111827; }
    </style>
</head>
<body class="min-h-screen p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-primary-text mb-8 text-center">
            🧪 Teste do App Segurança
        </h1>
        
        <!-- Dashboard Button -->
        <div class="mb-8 text-center">
            <button class="dashboard-btn p-4 rounded-lg flex flex-col items-center justify-center text-xs transition-all duration-300 transform hover:scale-105 bg-surface-elevated border border-border-color hover:border-red-500 hover:bg-surface-hover relative mx-auto">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 mb-2 text-red-500">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                </svg>
                <span class="font-medium text-primary-text">Segurança</span>
                <div id="security-alert-badge" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    <span id="security-alert-count">3</span>
                </div>
            </button>
        </div>
        
        <!-- Seção de Segurança -->
        <div class="max-w-4xl mx-auto p-6">
            <h2 class="text-2xl font-bold text-primary-text mb-6 flex items-center gap-3">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8 text-red-500">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.623 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
                </svg>
                Centro de Segurança
            </h2>

            <!-- Status de Segurança -->
            <div class="bg-surface-elevated border border-border-color rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-primary-text mb-4 flex items-center gap-2">
                    <svg class="w-6 h-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Status de Segurança
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-orange-400 mb-1">MÉDIO</div>
                        <div class="text-secondary-text text-sm">Nível de Ameaça</div>
                    </div>
                    
                    <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-blue-400 mb-1">75/100</div>
                        <div class="text-secondary-text text-sm">Score de Defesa</div>
                    </div>
                    
                    <div class="bg-surface-default border border-border-color rounded-lg p-4 text-center">
                        <div class="text-2xl font-bold text-purple-400 mb-1">3</div>
                        <div class="text-secondary-text text-sm">Invasões Ativas</div>
                    </div>
                </div>
            </div>

            <!-- Invasões Detectadas -->
            <div class="bg-surface-elevated border border-border-color rounded-xl p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-primary-text flex items-center gap-2">
                        <svg class="w-6 h-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
                        </svg>
                        Invasões Detectadas
                        <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">3</span>
                    </h3>
                    <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        🛡️ Bloquear Todas
                    </button>
                </div>
                <div class="space-y-3">
                    <!-- Invasão Crítica -->
                    <div class="border border-red-500 bg-red-500/10 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
                                <div>
                                    <h4 class="font-semibold text-primary-text">HackerPro123</h4>
                                    <p class="text-sm text-secondary-text">UID: abc123def456</p>
                                    <div class="flex items-center gap-2 mt-1">
                                        <span class="text-xs px-2 py-1 rounded bg-red-500 text-white">CRÍTICO</span>
                                        <span class="text-xs px-2 py-1 rounded bg-purple-500 text-white">BRUTEFORCE ATIVO</span>
                                        <span class="text-xs px-2 py-1 rounded bg-red-600 text-white">ACESSO BANCÁRIO</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-bold text-red-400">15:30</div>
                                <div class="text-xs text-secondary-text mb-2">Tempo restante</div>
                                <button class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                    🛡️ Bloquear
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Invasão Alta -->
                    <div class="border border-orange-500 bg-orange-500/10 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-3 h-3 bg-orange-400 rounded-full animate-pulse"></div>
                                <div>
                                    <h4 class="font-semibold text-primary-text">CyberNinja</h4>
                                    <p class="text-sm text-secondary-text">UID: def456ghi789</p>
                                    <div class="flex items-center gap-2 mt-1">
                                        <span class="text-xs px-2 py-1 rounded bg-orange-500 text-white">ALTO</span>
                                        <span class="text-xs px-2 py-1 rounded bg-purple-500 text-white">BRUTEFORCE ATIVO</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-bold text-orange-400">22:45</div>
                                <div class="text-xs text-secondary-text mb-2">Tempo restante</div>
                                <button class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                    🛡️ Bloquear
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Invasão Média -->
                    <div class="border border-yellow-500 bg-yellow-500/10 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                                <div>
                                    <h4 class="font-semibold text-primary-text">ScriptKiddie</h4>
                                    <p class="text-sm text-secondary-text">UID: ghi789jkl012</p>
                                    <div class="flex items-center gap-2 mt-1">
                                        <span class="text-xs px-2 py-1 rounded bg-yellow-500 text-white">MÉDIO</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-bold text-yellow-400">08:15</div>
                                <div class="text-xs text-secondary-text mb-2">Tempo restante</div>
                                <button class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors">
                                    🛡️ Bloquear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ações de Defesa -->
            <div class="bg-surface-elevated border border-border-color rounded-xl p-6">
                <h3 class="text-lg font-semibold text-primary-text mb-4 flex items-center gap-2">
                    <svg class="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                    </svg>
                    Ações de Defesa
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button class="bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg font-medium transition-colors flex items-center gap-3">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Fechar Todas Minhas Conexões
                    </button>
                    <button class="bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg font-medium transition-colors flex items-center gap-3">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                        </svg>
                        Bloqueio de Emergência
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Informações sobre o teste -->
        <div class="mt-8 bg-surface-elevated border border-border-color rounded-lg p-6">
            <h2 class="text-xl font-bold text-primary-text mb-4">📋 Status do Teste</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h3 class="font-semibold text-green-400 mb-2">✅ Interface Implementada</h3>
                    <ul class="space-y-1 text-secondary-text">
                        <li>• Botão no dashboard com badge</li>
                        <li>• Status de segurança</li>
                        <li>• Lista de invasões</li>
                        <li>• Ações de defesa</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold text-blue-400 mb-2">🔧 Funcionalidades</h3>
                    <ul class="space-y-1 text-secondary-text">
                        <li>• Detecção de invasões</li>
                        <li>• Classificação por ameaça</li>
                        <li>• Bloqueio individual</li>
                        <li>• Bloqueio em massa</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
