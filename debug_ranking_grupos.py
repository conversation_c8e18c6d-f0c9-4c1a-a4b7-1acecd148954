#!/usr/bin/env python3
"""
DEBUG ESPECÍFICO PARA RANKING DE GRUPOS
Vamos descobrir por que os grupos não aparecem
"""

print("🔍 DEBUG RANKING GRUPOS")
print("=" * 30)

try:
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent))
    
    from game.tournament_ttl import TorneioTTLManager
    from database.supabase_client import supabase_client
    import uuid
    
    # Inicializar manager
    ttl_manager = TorneioTTLManager()
    
    # Obter cliente Supabase
    supabase = supabase_client.client
    
    # 1. Testar RPC diretamente
    print("1️⃣ Testando RPC get_ranking_torneio diretamente...")
    
    resultado_rpc = supabase.rpc('get_ranking_torneio', {
        'tipo_param': 'deface',
        'limite': 10
    }).execute()
    
    print(f"✅ Resultado RPC direto: {resultado_rpc.data}")
    
    # 2. Testar busca de dados do jogador
    if resultado_rpc.data:
        primeiro_uid = resultado_rpc.data[0]['jogador_uid']
        print(f"\n2️⃣ Testando busca de dados para UID: {primeiro_uid}")
        
        try:
            from game import new_models as models
            jogador = models.get_jogador(primeiro_uid)
            print(f"✅ Dados do jogador: {jogador}")
        except Exception as e:
            print(f"❌ Erro ao buscar jogador: {e}")
    
    # 3. Testar função get_ranking_torneio do TTL
    print(f"\n3️⃣ Testando get_ranking_torneio TTL...")
    ranking_ttl = ttl_manager.get_ranking_torneio('deface', 'individual', 10)
    print(f"✅ Ranking TTL: {ranking_ttl}")
    
    # 4. Testar função get_ranking_deface completa
    print(f"\n4️⃣ Testando get_ranking_deface completa...")
    ranking_deface = ttl_manager.get_ranking_deface(10)
    print(f"✅ Ranking deface: {ranking_deface}")
    
    # 5. Verificar se existem dados na tabela torneio_pontuacoes
    print(f"\n5️⃣ Verificando dados na tabela diretamente...")
    dados_tabela = supabase.table('torneio_pontuacoes').select('*').eq('tipo_torneio', 'deface').execute()
    print(f"✅ Dados na tabela: {dados_tabela.data}")
    
    # 6. Verificar se existem grupos na tabela usuarios
    print(f"\n6️⃣ Verificando grupos na tabela usuarios...")
    usuarios_com_grupo = supabase.table('usuarios').select('uid, nick, grupo_id, grupo_nome').limit(5).execute()
    print(f"✅ Usuários com grupo: {usuarios_com_grupo.data}")
    
    print("\n🎯 DEBUG COMPLETO!")
    
except Exception as e:
    print(f"❌ Erro: {e}")
    import traceback
    traceback.print_exc()
