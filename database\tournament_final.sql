-- ==========================================
-- SISTEMA DE TORNEIOS TTL - VERSÃO DEFINITIVA
-- Execute estes comandos no SQL Editor do Supabase
-- ==========================================

-- ============ LIMPEZA INICIAL ============
-- Limpar tudo antes de recriar (evita conflitos)
DROP TABLE IF EXISTS torneio_pontuacoes CASCADE;
DROP TABLE IF EXISTS torneio_recompensas_config CASCADE;
DROP TABLE IF EXISTS torneios_ativos CASCADE;

-- ============ STEP 1: TABELAS BÁSICAS ============

-- Tabela de torneios ativos
CREATE TABLE torneios_ativos (
    id SERIAL PRIMARY KEY,
    tipo VARCHAR(50) NOT NULL,
    ativo BOOLEAN DEFAULT true,
    data_inicio TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    data_fim TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de pontuações com TTL
CREATE TABLE torneio_pontuacoes (
    id SERIAL PRIMARY KEY,
    torneio_id INTEGER,
    jogador_uid VARCHAR(100) NOT NULL,
    pontos_individuais INTEGER DEFAULT 0,
    pontos_grupo INTEGER DEFAULT 0,
    tipo_torneio VARCHAR(50) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Tabela de configuração de recompensas
CREATE TABLE torneio_recompensas_config (
    id SERIAL PRIMARY KEY,
    tipo_torneio VARCHAR(50) NOT NULL,
    posicao_min INTEGER NOT NULL,
    posicao_max INTEGER NOT NULL,
    dinheiro INTEGER DEFAULT 0,
    shacks INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ============ STEP 2: FUNÇÃO TTL ============

-- Função para atualizar TTL automaticamente
CREATE OR REPLACE FUNCTION atualizar_ttl_automatico()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Atualizar TTL baseado no tipo de torneio
    CASE NEW.tipo_torneio
        WHEN 'deface' THEN
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '24 hours';
        WHEN 'upgrade' THEN
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '168 hours';
        ELSE
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '24 hours';
    END CASE;
    
    RETURN NEW;
END;
$$;

-- ============ STEP 3: TRIGGER ============

-- Trigger para TTL automático
CREATE TRIGGER trigger_ttl_automatico
    BEFORE INSERT OR UPDATE ON torneio_pontuacoes
    FOR EACH ROW
    EXECUTE FUNCTION atualizar_ttl_automatico();

-- ============ STEP 4: ÍNDICES ============

CREATE INDEX idx_torneios_tipo ON torneios_ativos(tipo);
CREATE INDEX idx_pontuacoes_jogador ON torneio_pontuacoes(jogador_uid);
CREATE INDEX idx_pontuacoes_tipo ON torneio_pontuacoes(tipo_torneio);
CREATE INDEX idx_pontuacoes_expires ON torneio_pontuacoes(expires_at);

-- ============ STEP 5: RECOMPENSAS ============

-- Recompensas deface
INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 1, 1, 50000, 500);

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 2, 2, 30000, 300);

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 3, 3, 20000, 200);

-- Recompensas upgrade
INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 1, 1, 40000, 400);

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 2, 2, 25000, 250);

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 3, 3, 15000, 150);

-- ============ STEP 6: TORNEIOS INICIAIS ============

-- Torneio de deface (24h)
INSERT INTO torneios_ativos (tipo, data_fim)
VALUES ('deface', CURRENT_TIMESTAMP + INTERVAL '24 hours');

-- Torneio de upgrade (7 dias)
INSERT INTO torneios_ativos (tipo, data_fim)
VALUES ('upgrade', CURRENT_TIMESTAMP + INTERVAL '7 days');

-- ==========================================
-- SETUP DEFINITIVO - SISTEMA TTL PRONTO! 🚀
-- ==========================================
