-- ==========================================
-- CORREÇÃO DA TABELA BRUTEFORCE_ATAQUES
-- Execute estes comandos no SQL Editor do Supabase
-- ==========================================

-- Verificar se a tabela existe e adicionar colunas necessárias
CREATE TABLE IF NOT EXISTS bruteforce_ataques (
    id SERIAL PRIMARY KEY,
    atacante_uid VARCHAR(100) NOT NULL,
    alvo_ip VARCHAR(50) NOT NULL,
    alvo_nick VARCHAR(100),
    alvo_uid VARCHAR(100),
    tempo_total INTEGER NOT NULL,
    chance_sucesso FLOAT NOT NULL,
    inicio TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    fim_estimado TIMESTAMP WITH TIME ZONE NOT NULL,
    finalizado_em TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'executando',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Adicionar coluna atacante_acess se não existir (default FALSE)
ALTER TABLE bruteforce_ataques 
ADD COLUMN IF NOT EXISTS atacante_acess BOOLEAN DEFAULT FALSE;

-- Adicionar coluna alvo_dados se não existir (para armazenar dados do alvo)
ALTER TABLE bruteforce_ataques 
ADD COLUMN IF NOT EXISTS alvo_dados JSONB;

-- Adicionar coluna sucesso_confirmado se não existir (para compatibilidade)
ALTER TABLE bruteforce_ataques 
ADD COLUMN IF NOT EXISTS sucesso_confirmado BOOLEAN DEFAULT FALSE;

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_bruteforce_atacante_alvo 
ON bruteforce_ataques(atacante_uid, alvo_ip);

CREATE INDEX IF NOT EXISTS idx_bruteforce_status 
ON bruteforce_ataques(status);

CREATE INDEX IF NOT EXISTS idx_bruteforce_atacante_acess 
ON bruteforce_ataques(atacante_acess);

-- Comentários para documentação
COMMENT ON COLUMN bruteforce_ataques.atacante_acess IS 'TRUE quando o bruteforce foi bem-sucedido e o atacante tem acesso ao banco do alvo';
COMMENT ON COLUMN bruteforce_ataques.alvo_dados IS 'Dados JSON do alvo (nick, dinheiro, etc.) quando o bruteforce é bem-sucedido';
COMMENT ON COLUMN bruteforce_ataques.sucesso_confirmado IS 'Compatibilidade - indica se o bruteforce foi confirmado como bem-sucedido';

-- Verificar estrutura da tabela
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'bruteforce_ataques' 
ORDER BY ordinal_position;
