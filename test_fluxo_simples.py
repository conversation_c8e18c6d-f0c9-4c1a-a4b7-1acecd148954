#!/usr/bin/env python3
"""
Teste simples para verificar o fluxo do bruteforce com persistência
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game import new_models as models
from database.supabase_client import supabase_client

def verificar_fluxo_bruteforce():
    """Verifica se o fluxo básico está funcionando"""
    
    print("🔍 VERIFICANDO FLUXO DO BRUTEFORCE")
    print("=" * 50)
    
    try:
        # 1. Verificar conexão
        if not supabase_client.is_connected():
            print("❌ Banco não conectado")
            return False
        print("✅ Banco conectado")
        
        # 2. Verificar se a coluna atacante_acess existe na conexoes_ativas
        try:
            result = supabase_client.client.table('conexoes_ativas').select('id, atacante_acess').limit(1).execute()
            print("✅ Coluna atacante_acess existe na conexoes_ativas")
        except Exception as e:
            if "atacante_acess" in str(e):
                print("❌ Coluna atacante_acess NÃO existe na conexoes_ativas")
                print("📋 Execute: ALTER TABLE conexoes_ativas ADD COLUMN IF NOT EXISTS atacante_acess BOOLEAN DEFAULT FALSE;")
                return False
            else:
                print(f"⚠️ Erro ao verificar coluna: {e}")
        
        # 3. Verificar função de verificação de status
        print("\n🔍 Testando função de verificação de status...")
        status = models.verificar_status_bruteforce_conexao("test_user", "192.168.1.1")
        print(f"✅ Função de verificação funcionando: {status.get('status', 'N/A')}")
        
        # 4. Verificar estrutura das conexões
        print("\n🔍 Verificando estrutura das conexões...")
        conexoes = supabase_client.client.table('conexoes_ativas').select('*').limit(1).execute()
        if conexoes.data:
            conexao = conexoes.data[0]
            tem_atacante_acess = 'atacante_acess' in conexao
            print(f"✅ Conexão encontrada com atacante_acess: {tem_atacante_acess}")
            if tem_atacante_acess:
                print(f"   Valor atual: {conexao.get('atacante_acess', 'N/A')}")
        else:
            print("ℹ️ Nenhuma conexão ativa encontrada")
        
        print("\n" + "=" * 50)
        print("🎉 VERIFICAÇÃO COMPLETA!")
        print("✅ Sistema pronto para usar o fluxo:")
        print("   1. Explorar alvo → Criar conexão")
        print("   2. Terminal → Bruteforce")
        print("   3. Sucesso → atacante_acess=TRUE")
        print("   4. Botão 'Acessar Banco' aparece")
        print("   5. Acesso persiste enquanto conexão ativa")
        print("   6. Fechar conexão → acesso removido")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERRO: {str(e)}")
        return False

if __name__ == "__main__":
    verificar_fluxo_bruteforce()
