-- ==========================================
-- CORREÇÃO DAS TABELAS TTL
-- Execute no SQL Editor do Supabase
-- ==========================================

-- 1. RENOMEAR tabela de recompensas (se necessário)
-- Se a tabela se chama "torneio_recompensas", renomeie para "torneio_recompensas_config"
ALTER TABLE torneio_recompensas RENAME TO torneio_recompensas_config;

-- 2. VERIFICAR se as colunas estão corretas na tabela torneio_pontuacoes
-- Caso não existam, adicionar as colunas necessárias:

-- Adicionar colunas se não existirem
ALTER TABLE torneio_pontuacoes 
ADD COLUMN IF NOT EXISTS torneio_id INTEGER,
ADD COLUMN IF NOT EXISTS jogador_uid VARCHAR(100) NOT NULL DEFAULT '',
ADD COLUMN IF NOT EXISTS pontos_individuais INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS pontos_grupo INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS tipo_torneio VARCHAR(50) NOT NULL DEFAULT '',
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (CURRENT_TIMESTAMP + INTERVAL '24 hours'),
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;

-- 3. CRIAR função TTL se não existir
CREATE OR REPLACE FUNCTION atualizar_ttl_automatico()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Atualizar TTL baseado no tipo de torneio
    CASE NEW.tipo_torneio
        WHEN 'deface' THEN
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '24 hours';
        WHEN 'upgrade' THEN
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '168 hours';
        ELSE
            NEW.expires_at := CURRENT_TIMESTAMP + INTERVAL '24 hours';
    END CASE;
    
    RETURN NEW;
END;
$$;

-- 4. CRIAR trigger se não existir
DROP TRIGGER IF EXISTS trigger_ttl_automatico ON torneio_pontuacoes;
CREATE TRIGGER trigger_ttl_automatico
    BEFORE INSERT OR UPDATE ON torneio_pontuacoes
    FOR EACH ROW
    EXECUTE FUNCTION atualizar_ttl_automatico();

-- 5. CRIAR índices se não existirem
CREATE INDEX IF NOT EXISTS idx_torneios_tipo ON torneios_ativos(tipo);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_jogador ON torneio_pontuacoes(jogador_uid);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_tipo ON torneio_pontuacoes(tipo_torneio);
CREATE INDEX IF NOT EXISTS idx_pontuacoes_expires ON torneio_pontuacoes(expires_at);

-- 6. INSERIR recompensas se a tabela estiver vazia
INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 1, 1, 50000, 500)
ON CONFLICT DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 2, 2, 30000, 300)
ON CONFLICT DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('deface', 3, 3, 20000, 200)
ON CONFLICT DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 1, 1, 40000, 400)
ON CONFLICT DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 2, 2, 25000, 250)
ON CONFLICT DO NOTHING;

INSERT INTO torneio_recompensas_config (tipo_torneio, posicao_min, posicao_max, dinheiro, shacks) 
VALUES ('upgrade', 3, 3, 15000, 150)
ON CONFLICT DO NOTHING;

-- 7. INSERIR torneios ativos se não existirem
INSERT INTO torneios_ativos (tipo, data_fim)
SELECT 'deface', CURRENT_TIMESTAMP + INTERVAL '24 hours'
WHERE NOT EXISTS (SELECT 1 FROM torneios_ativos WHERE tipo = 'deface' AND ativo = true);

INSERT INTO torneios_ativos (tipo, data_fim)
SELECT 'upgrade', CURRENT_TIMESTAMP + INTERVAL '7 days'
WHERE NOT EXISTS (SELECT 1 FROM torneios_ativos WHERE tipo = 'upgrade' AND ativo = true);

-- ==========================================
-- CORREÇÃO CONCLUÍDA! 
-- Agora teste: python teste_ttl_tabelas.py
-- ==========================================
