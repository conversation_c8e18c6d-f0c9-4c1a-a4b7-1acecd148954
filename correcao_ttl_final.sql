-- =====================================================
-- SISTEMA TTL DE TORNEIOS - CORREÇÃO FINAL
-- =====================================================
-- Este SQL corrige os problemas encontrados no sistema TTL

-- 1. FUNÇÃO RPC para adicionar pontos de torneio
CREATE OR REPLACE FUNCTION adicionar_pontos_torneio(
    jogador_uid_param UUID,
    tipo_torneio_param TEXT,
    pontos_individuais_param INTEGER DEFAULT 0,
    pontos_grupo_param INTEGER DEFAULT 0,
    nick_param TEXT DEFAULT NULL,
    grupo_id_param UUID DEFAULT NULL,
    grupo_nome_param TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    resultado JSON;
    expires_timestamp TIMESTAMPTZ;
BEGIN
    -- Calcular TTL (24 horas a partir de agora)
    expires_timestamp := NOW() + INTERVAL '24 hours';
    
    -- Inserir pontos na tabela
    INSERT INTO torneio_pontuacoes (
        jogador_uid,
        tipo_torneio,
        pontos_individuais,
        pontos_grupo,
        nick,
        grupo_id,
        grupo_nome,
        created_at,
        expires_at
    ) VALUES (
        jogador_uid_param,
        tipo_torneio_param,
        pontos_individuais_param,
        pontos_grupo_param,
        nick_param,
        grupo_id_param,
        grupo_nome_param,
        NOW(),
        expires_timestamp
    );
    
    -- Retornar sucesso
    resultado := json_build_object(
        'sucesso', true,
        'mensagem', 'Pontos adicionados com sucesso',
        'pontos_adicionados', pontos_individuais_param + pontos_grupo_param,
        'expires_at', expires_timestamp
    );
    
    RETURN resultado;
    
EXCEPTION WHEN OTHERS THEN
    -- Retornar erro
    resultado := json_build_object(
        'sucesso', false,
        'mensagem', 'Erro ao adicionar pontos: ' || SQLERRM,
        'codigo_erro', SQLSTATE
    );
    
    RETURN resultado;
END;
$$;

-- 2. FUNÇÃO RPC para obter ranking
CREATE OR REPLACE FUNCTION get_ranking_torneio(
    tipo_param TEXT,
    limite INTEGER DEFAULT 50
)
RETURNS TABLE(
    jogador_uid UUID,
    nick TEXT,
    total_pontos BIGINT,
    posicao INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tp.jogador_uid,
        tp.nick,
        SUM(tp.pontos_individuais)::BIGINT as total_pontos,
        ROW_NUMBER() OVER (ORDER BY SUM(tp.pontos_individuais) DESC)::INTEGER as posicao
    FROM torneio_pontuacoes tp
    WHERE tp.tipo_torneio = tipo_param
      AND tp.expires_at > NOW()
    GROUP BY tp.jogador_uid, tp.nick
    ORDER BY total_pontos DESC
    LIMIT limite;
END;
$$;

-- 3. FUNÇÃO de limpeza automática TTL
CREATE OR REPLACE FUNCTION limpar_dados_expirados_ttl()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    linhas_removidas INTEGER;
BEGIN
    -- Remove dados expirados
    DELETE FROM torneio_pontuacoes 
    WHERE expires_at <= NOW();
    
    GET DIAGNOSTICS linhas_removidas = ROW_COUNT;
    
    RETURN linhas_removidas;
END;
$$;

-- 4. TRIGGER para limpeza automática
CREATE OR REPLACE FUNCTION trigger_limpeza_ttl()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Executar limpeza ocasionalmente (1% das vezes)
    IF random() < 0.01 THEN
        PERFORM limpar_dados_expirados_ttl();
    END IF;
    
    RETURN NEW;
END;
$$;

-- Criar o trigger
DROP TRIGGER IF EXISTS trigger_limpeza_automatica_ttl ON torneio_pontuacoes;
CREATE TRIGGER trigger_limpeza_automatica_ttl
    AFTER INSERT ON torneio_pontuacoes
    FOR EACH ROW
    EXECUTE FUNCTION trigger_limpeza_ttl();

-- 5. INSERIR dados iniciais nas tabelas de configuração se não existirem
-- Verificar e inserir apenas se as tabelas existirem e tiverem as colunas corretas

-- Inserir em torneios_ativos apenas se a tabela existir
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'torneios_ativos') THEN
        INSERT INTO torneios_ativos (id, ativo, created_at, updated_at)
        VALUES 
            (gen_random_uuid(), true, NOW(), NOW())
        ON CONFLICT DO NOTHING;
    END IF;
END $$;

-- Inserir em torneio_recompensas_config apenas se a tabela existir
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'torneio_recompensas_config') THEN
        INSERT INTO torneio_recompensas_config (id, tipo_torneio, posicao, recompensa_tipo, recompensa_valor, created_at, updated_at)
        VALUES 
            (gen_random_uuid(), 'deface', 1, 'dinheiro', 1000, NOW(), NOW()),
            (gen_random_uuid(), 'deface', 2, 'dinheiro', 500, NOW(), NOW()),
            (gen_random_uuid(), 'deface', 3, 'dinheiro', 250, NOW(), NOW()),
            (gen_random_uuid(), 'upgrade', 1, 'dinheiro', 1500, NOW(), NOW()),
            (gen_random_uuid(), 'upgrade', 2, 'dinheiro', 750, NOW(), NOW()),
            (gen_random_uuid(), 'upgrade', 3, 'dinheiro', 375, NOW(), NOW())
        ON CONFLICT DO NOTHING;
    END IF;
END $$;

-- 6. GRANTS de segurança
GRANT EXECUTE ON FUNCTION adicionar_pontos_torneio TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_ranking_torneio TO anon, authenticated;
GRANT EXECUTE ON FUNCTION limpar_dados_expirados_ttl TO authenticated;
